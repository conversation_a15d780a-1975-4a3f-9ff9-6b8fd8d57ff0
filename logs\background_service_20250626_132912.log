2025-06-26 13:29:12,709 - root - INFO - Loaded environment variables from .env file
2025-06-26 13:29:13,732 - root - INFO - Loaded 29 trade records from logs/trades\trade_log_********.json
2025-06-26 13:29:13,732 - root - INFO - Loaded 19 asset selection records from logs/trades\asset_selection_********.json
2025-06-26 13:29:13,732 - root - INFO - Trade logger initialized with log directory: logs/trades
2025-06-26 13:29:15,134 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 13:29:15,141 - root - INFO - Configuration loaded successfully.
2025-06-26 13:29:15,141 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 13:29:15,151 - root - INFO - Configuration loaded successfully.
2025-06-26 13:29:15,151 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 13:29:15,157 - root - INFO - Configuration loaded successfully.
2025-06-26 13:29:15,157 - root - INFO - Loading notification configuration from config/notifications_bitvavo.json...
2025-06-26 13:29:15,158 - root - INFO - Notification configuration loaded successfully.
2025-06-26 13:29:16,103 - root - INFO - Telegram command handlers registered
2025-06-26 13:29:16,111 - root - INFO - Telegram bot polling started
2025-06-26 13:29:16,111 - root - INFO - Telegram notifier initialized with notification level: standard
2025-06-26 13:29:16,111 - root - INFO - Telegram notification channel initialized
2025-06-26 13:29:16,116 - root - INFO - Successfully loaded templates using utf-8 encoding
2025-06-26 13:29:16,117 - root - INFO - Loaded 24 templates from file
2025-06-26 13:29:16,117 - root - INFO - Notification manager initialized with 1 channels
2025-06-26 13:29:16,117 - root - INFO - Notification manager initialized
2025-06-26 13:29:16,118 - root - INFO - Added critical time window: 23:55 for 10 minutes - Before daily candle close (1d)
2025-06-26 13:29:16,118 - root - INFO - Added critical time window: 00:00 for 10 minutes - After daily candle close (1d)
2025-06-26 13:29:16,119 - root - INFO - Set up critical time windows for 1d timeframe
2025-06-26 13:29:16,119 - root - INFO - Network watchdog initialized with 10s check interval
2025-06-26 13:29:16,122 - root - INFO - Loaded recovery state from data/state\recovery_state.json
2025-06-26 13:29:16,124 - root - INFO - No state file found at C:\Users\<USER>\OneDrive\Stalinis kompiuteris\Asset_Screener_Python\data\state\data/state\background_service_********_114325.json.json
2025-06-26 13:29:16,124 - root - INFO - Recovery manager initialized
2025-06-26 13:29:16,125 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 13:29:16,129 - root - INFO - Configuration loaded successfully.
2025-06-26 13:29:16,129 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 13:29:16,136 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 13:29:16,137 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 13:29:16,137 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 13:29:16,137 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 13:29:16,138 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 13:29:16,138 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 13:29:16,138 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 13:29:16,139 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 13:29:16,150 - root - INFO - Configuration loaded successfully.
2025-06-26 13:29:16,182 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getMe "HTTP/1.1 200 OK"
2025-06-26 13:29:16,196 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/deleteWebhook "HTTP/1.1 200 OK"
2025-06-26 13:29:16,197 - telegram.ext.Application - INFO - Application started
2025-06-26 13:29:16,481 - root - INFO - Successfully loaded markets for bitvavo.
2025-06-26 13:29:16,482 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 13:29:16,482 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 13:29:16,482 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 13:29:16,483 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 13:29:16,483 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 13:29:16,491 - root - INFO - Configuration loaded successfully.
2025-06-26 13:29:16,494 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 13:29:16,501 - root - INFO - Configuration loaded successfully.
2025-06-26 13:29:16,501 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 13:29:16,508 - root - INFO - Configuration loaded successfully.
2025-06-26 13:29:16,509 - root - INFO - Loaded paper trading history from paper_trading_history.json
2025-06-26 13:29:16,510 - root - INFO - Paper trading initialized with EUR as quote currency
2025-06-26 13:29:16,510 - root - INFO - Trading executor initialized for bitvavo
2025-06-26 13:29:16,510 - root - INFO - Trading mode: paper
2025-06-26 13:29:16,510 - root - INFO - Trading enabled: True
2025-06-26 13:29:16,510 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 13:29:16,510 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 13:29:16,510 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 13:29:16,512 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 13:29:16,512 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 13:29:16,518 - root - INFO - Configuration loaded successfully.
2025-06-26 13:29:16,751 - root - INFO - Successfully loaded markets for bitvavo.
2025-06-26 13:29:16,751 - root - INFO - Trading enabled in paper mode
2025-06-26 13:29:16,751 - root - INFO - Saved paper trading history to paper_trading_history.json
2025-06-26 13:29:16,751 - root - INFO - Reset paper trading account to initial balance: 100 EUR
2025-06-26 13:29:16,754 - root - INFO - Reset paper trading account to initial balance
2025-06-26 13:29:16,755 - root - INFO - Generated run ID: ********_132916
2025-06-26 13:29:16,755 - root - INFO - Ensured metrics directory exists: Performance_Metrics
2025-06-26 13:29:16,755 - root - INFO - Background service initialized
2025-06-26 13:29:16,755 - root - INFO - Network watchdog started
2025-06-26 13:29:16,755 - root - INFO - Network watchdog started
2025-06-26 13:29:16,759 - root - INFO - Schedule set up for 1d timeframe
2025-06-26 13:29:16,761 - root - INFO - Background service started
2025-06-26 13:29:16,761 - root - INFO - Executing strategy (run #1)...
2025-06-26 13:29:16,763 - root - INFO - Resetting daily trade counters for this strategy execution
2025-06-26 13:29:16,763 - root - INFO - No trades recorded today (Max: 5)
2025-06-26 13:29:16,763 - root - INFO - Initialized daily trades counter for 2025-06-26
2025-06-26 13:29:16,763 - root - INFO - Creating snapshot for candle timestamp: ********
2025-06-26 13:29:16,843 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 13:29:22,782 - root - WARNING - Failed to send with Markdown formatting: Timed out
2025-06-26 13:29:22,850 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 13:29:22,850 - root - INFO - Using trend method 'PGO For Loop' for strategy execution
2025-06-26 13:29:22,851 - root - INFO - Using configured start date for 1d timeframe: 2025-02-10
2025-06-26 13:29:22,851 - root - INFO - Using recent date for performance tracking: 2025-06-19
2025-06-26 13:29:22,853 - root - INFO - Using real-time data fetching - only fetching new data since last cached timestamp
2025-06-26 13:29:22,911 - root - INFO - Loaded 2137 rows of ETH/USDT data from cache (last updated: 2025-06-26)
2025-06-26 13:29:22,912 - root - INFO - Last timestamp in cache for ETH/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 13:29:22,913 - root - INFO - Expected last timestamp for ETH/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 13:29:22,913 - root - INFO - Data is up to date for ETH/USDT
2025-06-26 13:29:22,916 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 13:29:22,937 - root - INFO - Loaded 2137 rows of BTC/USDT data from cache (last updated: 2025-06-26)
2025-06-26 13:29:22,938 - root - INFO - Last timestamp in cache for BTC/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 13:29:22,939 - root - INFO - Expected last timestamp for BTC/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 13:29:22,939 - root - INFO - Data is up to date for BTC/USDT
2025-06-26 13:29:22,940 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 13:29:22,958 - root - INFO - Loaded 1780 rows of SOL/USDT data from cache (last updated: 2025-06-26)
2025-06-26 13:29:22,958 - root - INFO - Last timestamp in cache for SOL/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 13:29:22,959 - root - INFO - Expected last timestamp for SOL/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 13:29:22,959 - root - INFO - Data is up to date for SOL/USDT
2025-06-26 13:29:22,961 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 13:29:22,972 - root - INFO - Loaded 785 rows of SUI/USDT data from cache (last updated: 2025-06-26)
2025-06-26 13:29:22,973 - root - INFO - Last timestamp in cache for SUI/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 13:29:22,973 - root - INFO - Expected last timestamp for SUI/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 13:29:22,973 - root - INFO - Data is up to date for SUI/USDT
2025-06-26 13:29:22,974 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 13:29:22,993 - root - INFO - Loaded 2137 rows of XRP/USDT data from cache (last updated: 2025-06-26)
2025-06-26 13:29:22,994 - root - INFO - Last timestamp in cache for XRP/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 13:29:22,994 - root - INFO - Expected last timestamp for XRP/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 13:29:22,994 - root - INFO - Data is up to date for XRP/USDT
2025-06-26 13:29:22,995 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 13:29:23,008 - root - INFO - Loaded 1715 rows of AAVE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 13:29:23,008 - root - INFO - Last timestamp in cache for AAVE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 13:29:23,009 - root - INFO - Expected last timestamp for AAVE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 13:29:23,009 - root - INFO - Data is up to date for AAVE/USDT
2025-06-26 13:29:23,009 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 13:29:23,022 - root - INFO - Loaded 1738 rows of AVAX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 13:29:23,022 - root - INFO - Last timestamp in cache for AVAX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 13:29:23,023 - root - INFO - Expected last timestamp for AVAX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 13:29:23,023 - root - INFO - Data is up to date for AVAX/USDT
2025-06-26 13:29:23,023 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 13:29:23,037 - root - INFO - Loaded 2137 rows of ADA/USDT data from cache (last updated: 2025-06-26)
2025-06-26 13:29:23,037 - root - INFO - Last timestamp in cache for ADA/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 13:29:23,037 - root - INFO - Expected last timestamp for ADA/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 13:29:23,037 - root - INFO - Data is up to date for ADA/USDT
2025-06-26 13:29:23,038 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 13:29:23,055 - root - INFO - Loaded 2137 rows of LINK/USDT data from cache (last updated: 2025-06-26)
2025-06-26 13:29:23,055 - root - INFO - Last timestamp in cache for LINK/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 13:29:23,056 - root - INFO - Expected last timestamp for LINK/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 13:29:23,056 - root - INFO - Data is up to date for LINK/USDT
2025-06-26 13:29:23,057 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 13:29:23,076 - root - INFO - Loaded 2137 rows of TRX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 13:29:23,078 - root - INFO - Last timestamp in cache for TRX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 13:29:23,078 - root - INFO - Expected last timestamp for TRX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 13:29:23,079 - root - INFO - Data is up to date for TRX/USDT
2025-06-26 13:29:23,079 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 13:29:23,086 - root - INFO - Loaded 783 rows of PEPE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 13:29:23,087 - root - INFO - Last timestamp in cache for PEPE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 13:29:23,087 - root - INFO - Expected last timestamp for PEPE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 13:29:23,087 - root - INFO - Data is up to date for PEPE/USDT
2025-06-26 13:29:23,088 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 13:29:23,106 - root - INFO - Loaded 2137 rows of DOGE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 13:29:23,106 - root - INFO - Last timestamp in cache for DOGE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 13:29:23,107 - root - INFO - Expected last timestamp for DOGE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 13:29:23,107 - root - INFO - Data is up to date for DOGE/USDT
2025-06-26 13:29:23,108 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 13:29:23,126 - root - INFO - Loaded 2137 rows of BNB/USDT data from cache (last updated: 2025-06-26)
2025-06-26 13:29:23,127 - root - INFO - Last timestamp in cache for BNB/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 13:29:23,127 - root - INFO - Expected last timestamp for BNB/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 13:29:23,128 - root - INFO - Data is up to date for BNB/USDT
2025-06-26 13:29:23,131 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 13:29:23,153 - root - INFO - Loaded 1773 rows of DOT/USDT data from cache (last updated: 2025-06-26)
2025-06-26 13:29:23,154 - root - INFO - Last timestamp in cache for DOT/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 13:29:23,154 - root - INFO - Expected last timestamp for DOT/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 13:29:23,154 - root - INFO - Data is up to date for DOT/USDT
2025-06-26 13:29:23,155 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 13:29:23,157 - root - INFO - Using 14 trend assets (USDT) for analysis and 14 trading assets (EUR) for execution
2025-06-26 13:29:23,158 - root - INFO - MTPI Multi-Indicator Configuration:
2025-06-26 13:29:23,158 - root - INFO -   - Number of indicators: 8
2025-06-26 13:29:23,158 - root - INFO -   - Enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-26 13:29:23,158 - root - INFO -   - Combination method: consensus
2025-06-26 13:29:23,158 - root - INFO -   - Long threshold: 0.1
2025-06-26 13:29:23,158 - root - INFO -   - Short threshold: -0.1
2025-06-26 13:29:23,159 - root - INFO - === RUNNING STRATEGY FOR WEB USING TEST_ALLOCATION ===
2025-06-26 13:29:23,159 - root - INFO - Parameters: use_mtpi_signal=False, mtpi_indicator_type=PGO
2025-06-26 13:29:23,159 - root - INFO - mtpi_timeframe=1d, mtpi_pgo_length=35
2025-06-26 13:29:23,159 - root - INFO - mtpi_upper_threshold=1.1, mtpi_lower_threshold=-0.58
2025-06-26 13:29:23,159 - root - INFO - timeframe=1d, analysis_start_date='2025-02-10'
2025-06-26 13:29:23,160 - root - INFO - n_assets=1, use_weighted_allocation=False
2025-06-26 13:29:23,160 - root - INFO - Using provided trend method: PGO For Loop
2025-06-26 13:29:23,160 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 13:29:23,172 - root - INFO - Configuration loaded successfully.
2025-06-26 13:29:23,173 - root - INFO - Saving configuration to config/settings_bitvavo_eur.yaml...
2025-06-26 13:29:23,181 - root - INFO - Configuration saved successfully.
2025-06-26 13:29:23,181 - root - INFO - Trend detection assets (USDT): ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-26 13:29:23,182 - root - INFO - Number of trend detection assets: 14
2025-06-26 13:29:23,182 - root - INFO - Selected assets type: <class 'list'>
2025-06-26 13:29:23,183 - root - INFO - Trading execution assets (EUR): ['BTC/EUR', 'ETH/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 13:29:23,183 - root - INFO - Number of trading assets: 14
2025-06-26 13:29:23,183 - root - INFO - Trading assets type: <class 'list'>
2025-06-26 13:29:23,440 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 13:29:23,448 - root - INFO - Configuration loaded successfully.
2025-06-26 13:29:23,455 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 13:29:23,465 - root - INFO - Configuration loaded successfully.
2025-06-26 13:29:23,465 - root - INFO - Execution context: backtesting
2025-06-26 13:29:23,465 - root - INFO - Execution timing: candle_close
2025-06-26 13:29:23,466 - root - INFO - Ratio calculation method: independent
2025-06-26 13:29:23,466 - root - INFO - Asset trend PGO parameters: length=None, upper_threshold=None, lower_threshold=None
2025-06-26 13:29:23,466 - root - INFO - MTPI indicators override: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-26 13:29:23,466 - root - INFO - MTPI combination method override: consensus
2025-06-26 13:29:23,466 - root - INFO - MTPI long threshold override: 0.1
2025-06-26 13:29:23,466 - root - INFO - MTPI short threshold override: -0.1
2025-06-26 13:29:23,467 - root - INFO - Using standard warmup period of 60 days for equal allocation
2025-06-26 13:29:23,467 - root - INFO - Fetching data from 2024-12-12 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-26 13:29:23,468 - root - INFO - Loaded metadata for 48 assets
2025-06-26 13:29:23,469 - root - INFO - Loaded metadata for 48 assets
2025-06-26 13:29:23,469 - root - INFO - Loaded metadata for 48 assets
2025-06-26 13:29:23,470 - root - INFO - Loaded metadata for 48 assets
2025-06-26 13:29:23,470 - root - INFO - Loaded metadata for 48 assets
2025-06-26 13:29:23,471 - root - INFO - Loaded metadata for 48 assets
2025-06-26 13:29:23,471 - root - INFO - Loaded metadata for 48 assets
2025-06-26 13:29:23,471 - root - INFO - Loaded metadata for 48 assets
2025-06-26 13:29:23,471 - root - INFO - Loaded metadata for 48 assets
2025-06-26 13:29:23,472 - root - INFO - Loaded metadata for 48 assets
2025-06-26 13:29:23,472 - root - INFO - Loaded metadata for 48 assets
2025-06-26 13:29:23,472 - root - INFO - Loaded metadata for 48 assets
2025-06-26 13:29:23,472 - root - INFO - Loaded metadata for 48 assets
2025-06-26 13:29:23,474 - root - INFO - Loaded metadata for 48 assets
2025-06-26 13:29:23,474 - root - INFO - Checking cache for 14 symbols (1d)...
2025-06-26 13:29:23,497 - root - INFO - Loaded 196 rows of ETH/USDT data from cache (last updated: 2025-06-26)
2025-06-26 13:29:23,499 - root - INFO - Metadata for ETH/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 13:29:23,499 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 13:29:23,502 - root - INFO - Loaded 196 rows of ETH/USDT data from cache (after filtering).
2025-06-26 13:29:23,540 - root - INFO - Loaded 196 rows of BTC/USDT data from cache (last updated: 2025-06-26)
2025-06-26 13:29:23,545 - root - INFO - Metadata for BTC/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 13:29:23,546 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 13:29:23,546 - root - INFO - Loaded 196 rows of BTC/USDT data from cache (after filtering).
2025-06-26 13:29:23,571 - root - INFO - Loaded 196 rows of SOL/USDT data from cache (last updated: 2025-06-26)
2025-06-26 13:29:23,574 - root - INFO - Metadata for SOL/USDT shows data starting from 2020-08-11, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 13:29:23,575 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 13:29:23,575 - root - INFO - Loaded 196 rows of SOL/USDT data from cache (after filtering).
2025-06-26 13:29:23,586 - root - INFO - Loaded 196 rows of SUI/USDT data from cache (last updated: 2025-06-26)
2025-06-26 13:29:23,588 - root - INFO - Metadata for SUI/USDT shows data starting from 2023-05-03, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 13:29:23,588 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 13:29:23,589 - root - INFO - Loaded 196 rows of SUI/USDT data from cache (after filtering).
2025-06-26 13:29:23,604 - root - INFO - Loaded 196 rows of XRP/USDT data from cache (last updated: 2025-06-26)
2025-06-26 13:29:23,605 - root - INFO - Metadata for XRP/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 13:29:23,605 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 13:29:23,605 - root - INFO - Loaded 196 rows of XRP/USDT data from cache (after filtering).
2025-06-26 13:29:23,620 - root - INFO - Loaded 196 rows of AAVE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 13:29:23,622 - root - INFO - Metadata for AAVE/USDT shows data starting from 2020-10-15, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 13:29:23,622 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 13:29:23,622 - root - INFO - Loaded 196 rows of AAVE/USDT data from cache (after filtering).
2025-06-26 13:29:23,637 - root - INFO - Loaded 196 rows of AVAX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 13:29:23,638 - root - INFO - Metadata for AVAX/USDT shows data starting from 2020-09-22, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 13:29:23,638 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 13:29:23,638 - root - INFO - Loaded 196 rows of AVAX/USDT data from cache (after filtering).
2025-06-26 13:29:23,655 - root - INFO - Loaded 196 rows of ADA/USDT data from cache (last updated: 2025-06-26)
2025-06-26 13:29:23,656 - root - INFO - Metadata for ADA/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 13:29:23,656 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 13:29:23,656 - root - INFO - Loaded 196 rows of ADA/USDT data from cache (after filtering).
2025-06-26 13:29:23,674 - root - INFO - Loaded 196 rows of LINK/USDT data from cache (last updated: 2025-06-26)
2025-06-26 13:29:23,676 - root - INFO - Metadata for LINK/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 13:29:23,679 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 13:29:23,680 - root - INFO - Loaded 196 rows of LINK/USDT data from cache (after filtering).
2025-06-26 13:29:23,698 - root - INFO - Loaded 196 rows of TRX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 13:29:23,699 - root - INFO - Metadata for TRX/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 13:29:23,700 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 13:29:23,700 - root - INFO - Loaded 196 rows of TRX/USDT data from cache (after filtering).
2025-06-26 13:29:23,707 - root - INFO - Loaded 196 rows of PEPE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 13:29:23,709 - root - INFO - Metadata for PEPE/USDT shows data starting from 2023-05-05, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 13:29:23,712 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 13:29:23,713 - root - INFO - Loaded 196 rows of PEPE/USDT data from cache (after filtering).
2025-06-26 13:29:23,731 - root - INFO - Loaded 196 rows of DOGE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 13:29:23,733 - root - INFO - Metadata for DOGE/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 13:29:23,734 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 13:29:23,734 - root - INFO - Loaded 196 rows of DOGE/USDT data from cache (after filtering).
2025-06-26 13:29:23,755 - root - INFO - Loaded 196 rows of BNB/USDT data from cache (last updated: 2025-06-26)
2025-06-26 13:29:23,757 - root - INFO - Metadata for BNB/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 13:29:23,758 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 13:29:23,758 - root - INFO - Loaded 196 rows of BNB/USDT data from cache (after filtering).
2025-06-26 13:29:23,772 - root - INFO - Loaded 196 rows of DOT/USDT data from cache (last updated: 2025-06-26)
2025-06-26 13:29:23,775 - root - INFO - Metadata for DOT/USDT shows data starting from 2020-08-18, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 13:29:23,777 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 13:29:23,778 - root - INFO - Loaded 196 rows of DOT/USDT data from cache (after filtering).
2025-06-26 13:29:23,778 - root - INFO - All 14 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-26 13:29:23,779 - root - INFO - Asset ETH/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 13:29:23,779 - root - INFO - Asset BTC/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 13:29:23,779 - root - INFO - Asset SOL/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 13:29:23,781 - root - INFO - Asset SUI/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 13:29:23,781 - root - INFO - Asset XRP/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 13:29:23,781 - root - INFO - Asset AAVE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 13:29:23,781 - root - INFO - Asset AVAX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 13:29:23,782 - root - INFO - Asset ADA/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 13:29:23,782 - root - INFO - Asset LINK/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 13:29:23,782 - root - INFO - Asset TRX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 13:29:23,783 - root - INFO - Asset PEPE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 13:29:23,783 - root - INFO - Asset DOGE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 13:29:23,783 - root - INFO - Asset BNB/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 13:29:23,783 - root - INFO - Asset DOT/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 13:29:23,804 - root - INFO - Using standard MTPI warmup period of 120 days
2025-06-26 13:29:23,804 - root - INFO - Fetching MTPI signals from 2024-10-13 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-26 13:29:23,804 - root - INFO - Fetching MTPI signals using trend method: PGO For Loop
2025-06-26 13:29:23,804 - root - INFO - Using multi-indicator MTPI with config override: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-26 13:29:23,805 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 13:29:23,814 - root - INFO - Configuration loaded successfully.
2025-06-26 13:29:23,815 - root - INFO - Loaded MTPI multi-indicator configuration with 8 enabled indicators
2025-06-26 13:29:23,815 - root - INFO - Applying configuration overrides: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-26 13:29:23,815 - root - INFO - Override: enabled_indicators = ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-26 13:29:23,816 - root - INFO - Override: combination_method = consensus
2025-06-26 13:29:23,816 - root - INFO - Override: long_threshold = 0.1
2025-06-26 13:29:23,816 - root - INFO - Override: short_threshold = -0.1
2025-06-26 13:29:23,816 - root - INFO - Using MTPI configuration: indicators=['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], method=consensus, thresholds=(-0.1, 0.1)
2025-06-26 13:29:23,817 - root - INFO - Calculated appropriate limit for 1d timeframe: 500 candles (minimum 180.0 candles needed for 60 length indicator)
2025-06-26 13:29:23,817 - root - INFO - Using adjusted limit: 500 for max indicator length: 60
2025-06-26 13:29:23,817 - root - INFO - Checking cache for 1 symbols (1d)...
2025-06-26 13:29:23,832 - root - INFO - Loaded 256 rows of BTC/USDT data from cache (last updated: 2025-06-26)
2025-06-26 13:29:23,834 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 13:29:23,835 - root - INFO - Loaded 256 rows of BTC/USDT data from cache (after filtering).
2025-06-26 13:29:23,835 - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-26 13:29:23,835 - root - INFO - Fetched BTC data: 256 candles from 2024-10-13 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 13:29:23,835 - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-06-26 13:29:23,869 - root - INFO - Generated PGO Score signals: {-1: 104, 0: 34, 1: 118}
2025-06-26 13:29:23,870 - root - INFO - Generated pgo signals: 256 values
2025-06-26 13:29:23,870 - root - INFO - Generating Bollinger Band signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False, heikin_src=close
2025-06-26 13:29:23,870 - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-06-26 13:29:23,882 - root - INFO - Generated BB Score signals: {-1: 106, 0: 32, 1: 118}
2025-06-26 13:29:23,882 - root - INFO - Generated Bollinger Band signals: 256 values
2025-06-26 13:29:23,884 - root - INFO - Generated bollinger_bands signals: 256 values
2025-06-26 13:29:24,233 - root - INFO - Generated DWMA signals using Weighted SD method
2025-06-26 13:29:24,234 - root - INFO - Generated dwma_score signals: 256 values
2025-06-26 13:29:24,272 - root - INFO - Generated DEMA Supertrend signals
2025-06-26 13:29:24,273 - root - INFO - Signal distribution: {-1: 142, 0: 1, 1: 113}
2025-06-26 13:29:24,273 - root - INFO - Generated DEMA Super Score signals
2025-06-26 13:29:24,274 - root - INFO - Generated dema_super_score signals: 256 values
2025-06-26 13:29:24,380 - root - INFO - Generated DPSD signals
2025-06-26 13:29:24,380 - root - INFO - Signal distribution: {-1: 98, 0: 87, 1: 71}
2025-06-26 13:29:24,380 - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-06-26 13:29:24,381 - root - INFO - Generated dpsd_score signals: 256 values
2025-06-26 13:29:24,394 - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-06-26 13:29:24,395 - root - INFO - Generated AAD Score signals using SMA method
2025-06-26 13:29:24,395 - root - INFO - Generated aad_score signals: 256 values
2025-06-26 13:29:24,445 - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-06-26 13:29:24,446 - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-06-26 13:29:24,446 - root - INFO - Generated dynamic_ema_score signals: 256 values
2025-06-26 13:29:24,529 - root - INFO - Generated quantile_dema_score signals: 256 values
2025-06-26 13:29:24,534 - root - INFO - Combined 8 signals using arithmetic mean aggregation
2025-06-26 13:29:24,535 - root - INFO - Signal distribution: {1: 145, -1: 110, 0: 1}
2025-06-26 13:29:24,535 - root - INFO - Generated combined MTPI signals: 256 values using consensus method
2025-06-26 13:29:24,535 - root - INFO - Signal distribution: {1: 145, -1: 110, 0: 1}
2025-06-26 13:29:24,536 - root - INFO - Calculating daily scores using trend method: PGO For Loop...
2025-06-26 13:29:24,538 - root - INFO - Saving configuration to config/settings.yaml...
2025-06-26 13:29:24,547 - root - INFO - Configuration saved successfully.
2025-06-26 13:29:24,547 - root - INFO - Updated config with trend method: PGO For Loop and PGO parameters
2025-06-26 13:29:24,547 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 13:29:24,554 - root - INFO - Configuration loaded successfully.
2025-06-26 13:29:24,554 - root - INFO - Using ratio-based approach with PGO (PGO For Loop)...
2025-06-26 13:29:24,554 - root - INFO - Calculating ratio PGO signals for 14 assets with PGO(35) using full OHLCV data
2025-06-26 13:29:24,554 - root - INFO - Using ratio calculation method: independent
2025-06-26 13:29:24,578 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:24,597 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BTC/USDT using full OHLCV data
2025-06-26 13:29:24,614 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 13:29:24,615 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:24,632 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 13:29:24,638 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SOL/USDT using full OHLCV data
2025-06-26 13:29:24,658 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 13:29:24,658 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:24,679 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 13:29:24,682 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SUI/USDT using full OHLCV data
2025-06-26 13:29:24,701 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 13:29:24,701 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:24,714 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 13:29:24,721 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/XRP/USDT using full OHLCV data
2025-06-26 13:29:24,738 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 13:29:24,739 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:24,754 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 13:29:24,760 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AAVE/USDT using full OHLCV data
2025-06-26 13:29:24,776 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-26 13:29:24,776 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:24,796 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-26 13:29:24,801 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AVAX/USDT using full OHLCV data
2025-06-26 13:29:24,824 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 13:29:24,825 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:24,841 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 13:29:24,847 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/ADA/USDT using full OHLCV data
2025-06-26 13:29:24,864 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 13:29:24,864 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:24,881 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 13:29:24,887 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/LINK/USDT using full OHLCV data
2025-06-26 13:29:24,904 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 13:29:24,904 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:24,926 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 13:29:24,932 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/TRX/USDT using full OHLCV data
2025-06-26 13:29:24,952 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 13:29:24,952 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:24,970 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 13:29:24,980 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/PEPE/USDT using full OHLCV data
2025-06-26 13:29:25,001 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 13:29:25,001 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:25,017 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 13:29:25,023 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOGE/USDT using full OHLCV data
2025-06-26 13:29:25,044 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:25,070 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BNB/USDT using full OHLCV data
2025-06-26 13:29:25,091 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 13:29:25,092 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:25,111 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 13:29:25,120 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOT/USDT using full OHLCV data
2025-06-26 13:29:25,144 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 13:29:25,144 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:25,164 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 13:29:25,170 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ETH/USDT using full OHLCV data
2025-06-26 13:29:25,189 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:25,218 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SOL/USDT using full OHLCV data
2025-06-26 13:29:25,237 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 13:29:25,237 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:25,255 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 13:29:25,262 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SUI/USDT using full OHLCV data
2025-06-26 13:29:25,280 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 13:29:25,280 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:25,298 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 13:29:25,301 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/XRP/USDT using full OHLCV data
2025-06-26 13:29:25,321 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 13:29:25,321 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:25,327 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 13:29:25,343 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AAVE/USDT using full OHLCV data
2025-06-26 13:29:25,365 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 13:29:25,365 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:25,380 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 13:29:25,387 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AVAX/USDT using full OHLCV data
2025-06-26 13:29:25,405 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 13:29:25,406 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:25,422 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 13:29:25,429 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ADA/USDT using full OHLCV data
2025-06-26 13:29:25,448 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 13:29:25,448 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:25,467 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 13:29:25,473 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/LINK/USDT using full OHLCV data
2025-06-26 13:29:25,499 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 13:29:25,499 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:25,519 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 13:29:25,523 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/TRX/USDT using full OHLCV data
2025-06-26 13:29:25,544 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 13:29:25,544 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:25,609 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 13:29:25,621 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/PEPE/USDT using full OHLCV data
2025-06-26 13:29:25,636 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:25,655 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOGE/USDT using full OHLCV data
2025-06-26 13:29:25,672 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:25,695 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/BNB/USDT using full OHLCV data
2025-06-26 13:29:25,715 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 13:29:25,715 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:25,731 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 13:29:25,735 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOT/USDT using full OHLCV data
2025-06-26 13:29:25,751 - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-06-26 13:29:25,752 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:25,766 - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-06-26 13:29:25,772 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ETH/USDT using full OHLCV data
2025-06-26 13:29:25,787 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:25,805 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BTC/USDT using full OHLCV data
2025-06-26 13:29:25,820 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 13:29:25,820 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:25,835 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 13:29:25,841 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/SUI/USDT using full OHLCV data
2025-06-26 13:29:25,862 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 13:29:25,862 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:25,876 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 13:29:25,883 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/XRP/USDT using full OHLCV data
2025-06-26 13:29:25,899 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-26 13:29:25,899 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:25,913 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-26 13:29:25,918 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AAVE/USDT using full OHLCV data
2025-06-26 13:29:25,933 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 13:29:25,933 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:25,947 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 13:29:25,952 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AVAX/USDT using full OHLCV data
2025-06-26 13:29:25,969 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 13:29:25,969 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:25,984 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 13:29:25,988 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ADA/USDT using full OHLCV data
2025-06-26 13:29:26,004 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 13:29:26,005 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:26,019 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 13:29:26,026 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/LINK/USDT using full OHLCV data
2025-06-26 13:29:26,042 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 13:29:26,042 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:26,057 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 13:29:26,063 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/TRX/USDT using full OHLCV data
2025-06-26 13:29:26,079 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 13:29:26,080 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:26,095 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 13:29:26,099 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/PEPE/USDT using full OHLCV data
2025-06-26 13:29:26,115 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 13:29:26,115 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:26,130 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 13:29:26,135 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOGE/USDT using full OHLCV data
2025-06-26 13:29:26,153 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 13:29:26,153 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:26,170 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 13:29:26,174 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BNB/USDT using full OHLCV data
2025-06-26 13:29:26,194 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 13:29:26,195 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:26,214 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 13:29:26,220 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOT/USDT using full OHLCV data
2025-06-26 13:29:26,237 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 13:29:26,237 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:26,238 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:29:26,253 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 13:29:26,263 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ETH/USDT using full OHLCV data
2025-06-26 13:29:26,280 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:26,302 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BTC/USDT using full OHLCV data
2025-06-26 13:29:26,322 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:26,348 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/SOL/USDT using full OHLCV data
2025-06-26 13:29:26,374 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:26,396 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/XRP/USDT using full OHLCV data
2025-06-26 13:29:26,418 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 13:29:26,418 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:26,435 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 13:29:26,441 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AAVE/USDT using full OHLCV data
2025-06-26 13:29:26,460 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:26,481 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AVAX/USDT using full OHLCV data
2025-06-26 13:29:26,501 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:26,523 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ADA/USDT using full OHLCV data
2025-06-26 13:29:26,546 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 13:29:26,546 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:26,562 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 13:29:26,567 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/LINK/USDT using full OHLCV data
2025-06-26 13:29:26,583 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:26,605 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/TRX/USDT using full OHLCV data
2025-06-26 13:29:26,627 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:26,645 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/PEPE/USDT using full OHLCV data
2025-06-26 13:29:26,664 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 13:29:26,664 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:26,680 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 13:29:26,684 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOGE/USDT using full OHLCV data
2025-06-26 13:29:26,701 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:26,719 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BNB/USDT using full OHLCV data
2025-06-26 13:29:26,734 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:26,752 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOT/USDT using full OHLCV data
2025-06-26 13:29:26,768 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 13:29:26,769 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:26,783 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 13:29:26,787 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ETH/USDT using full OHLCV data
2025-06-26 13:29:26,802 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:26,822 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BTC/USDT using full OHLCV data
2025-06-26 13:29:26,839 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:26,857 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SOL/USDT using full OHLCV data
2025-06-26 13:29:26,880 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 13:29:26,881 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:26,898 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 13:29:26,903 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SUI/USDT using full OHLCV data
2025-06-26 13:29:26,918 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 13:29:26,918 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:26,932 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 13:29:26,936 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AAVE/USDT using full OHLCV data
2025-06-26 13:29:26,953 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 13:29:26,953 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:26,973 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 13:29:26,980 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AVAX/USDT using full OHLCV data
2025-06-26 13:29:26,997 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 13:29:26,997 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:27,012 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 13:29:27,018 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ADA/USDT using full OHLCV data
2025-06-26 13:29:27,032 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 13:29:27,033 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:27,047 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 13:29:27,051 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/LINK/USDT using full OHLCV data
2025-06-26 13:29:27,067 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 13:29:27,068 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:27,081 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 13:29:27,088 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/TRX/USDT using full OHLCV data
2025-06-26 13:29:27,108 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 13:29:27,109 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:27,129 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 13:29:27,134 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/PEPE/USDT using full OHLCV data
2025-06-26 13:29:27,150 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:27,168 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOGE/USDT using full OHLCV data
2025-06-26 13:29:27,184 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:27,203 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BNB/USDT using full OHLCV data
2025-06-26 13:29:27,220 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 13:29:27,221 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:27,236 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 13:29:27,240 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOT/USDT using full OHLCV data
2025-06-26 13:29:27,263 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:27,282 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ETH/USDT using full OHLCV data
2025-06-26 13:29:27,297 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:27,317 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BTC/USDT using full OHLCV data
2025-06-26 13:29:27,333 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:27,353 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SOL/USDT using full OHLCV data
2025-06-26 13:29:27,368 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:27,388 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SUI/USDT using full OHLCV data
2025-06-26 13:29:27,407 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:27,432 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/XRP/USDT using full OHLCV data
2025-06-26 13:29:27,449 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 13:29:27,449 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:27,465 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 13:29:27,474 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/AVAX/USDT using full OHLCV data
2025-06-26 13:29:27,492 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 13:29:27,492 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:27,508 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 13:29:27,515 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ADA/USDT using full OHLCV data
2025-06-26 13:29:27,531 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 13:29:27,532 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:27,548 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 13:29:27,555 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/LINK/USDT using full OHLCV data
2025-06-26 13:29:27,580 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:27,604 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/TRX/USDT using full OHLCV data
2025-06-26 13:29:27,624 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:27,647 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/PEPE/USDT using full OHLCV data
2025-06-26 13:29:27,665 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 13:29:27,665 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:27,685 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 13:29:27,690 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOGE/USDT using full OHLCV data
2025-06-26 13:29:27,712 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:27,734 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BNB/USDT using full OHLCV data
2025-06-26 13:29:27,751 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 13:29:27,751 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:27,767 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 13:29:27,772 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOT/USDT using full OHLCV data
2025-06-26 13:29:27,791 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:27,812 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ETH/USDT using full OHLCV data
2025-06-26 13:29:27,828 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:27,849 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BTC/USDT using full OHLCV data
2025-06-26 13:29:27,867 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:27,885 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SOL/USDT using full OHLCV data
2025-06-26 13:29:27,901 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:27,921 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SUI/USDT using full OHLCV data
2025-06-26 13:29:27,938 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:27,961 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/XRP/USDT using full OHLCV data
2025-06-26 13:29:27,980 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 13:29:27,981 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:27,994 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 13:29:28,000 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/AAVE/USDT using full OHLCV data
2025-06-26 13:29:28,016 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:28,068 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ADA/USDT using full OHLCV data
2025-06-26 13:29:28,105 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-26 13:29:28,106 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:28,122 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-26 13:29:28,129 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/LINK/USDT using full OHLCV data
2025-06-26 13:29:28,147 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:28,169 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/TRX/USDT using full OHLCV data
2025-06-26 13:29:28,188 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:28,210 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/PEPE/USDT using full OHLCV data
2025-06-26 13:29:28,229 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:28,248 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOGE/USDT using full OHLCV data
2025-06-26 13:29:28,265 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:28,284 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BNB/USDT using full OHLCV data
2025-06-26 13:29:28,301 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:28,319 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOT/USDT using full OHLCV data
2025-06-26 13:29:28,337 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 13:29:28,337 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:28,351 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 13:29:28,356 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/ETH/USDT using full OHLCV data
2025-06-26 13:29:28,372 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:28,395 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BTC/USDT using full OHLCV data
2025-06-26 13:29:28,414 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:28,432 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SOL/USDT using full OHLCV data
2025-06-26 13:29:28,448 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 13:29:28,450 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:28,466 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 13:29:28,471 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SUI/USDT using full OHLCV data
2025-06-26 13:29:28,488 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:28,509 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/XRP/USDT using full OHLCV data
2025-06-26 13:29:28,525 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 13:29:28,526 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:28,548 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 13:29:28,553 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AAVE/USDT using full OHLCV data
2025-06-26 13:29:28,570 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 13:29:28,570 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:28,585 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 13:29:28,589 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AVAX/USDT using full OHLCV data
2025-06-26 13:29:28,607 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 13:29:28,609 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:28,624 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 13:29:28,630 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/LINK/USDT using full OHLCV data
2025-06-26 13:29:28,648 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:28,673 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/TRX/USDT using full OHLCV data
2025-06-26 13:29:28,698 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 13:29:28,699 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:28,714 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 13:29:28,721 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/PEPE/USDT using full OHLCV data
2025-06-26 13:29:28,739 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 13:29:28,739 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:28,755 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 13:29:28,765 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOGE/USDT using full OHLCV data
2025-06-26 13:29:28,782 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:28,804 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BNB/USDT using full OHLCV data
2025-06-26 13:29:28,824 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 13:29:28,825 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:28,847 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 13:29:28,852 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOT/USDT using full OHLCV data
2025-06-26 13:29:28,873 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:28,896 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ETH/USDT using full OHLCV data
2025-06-26 13:29:28,914 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:28,935 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BTC/USDT using full OHLCV data
2025-06-26 13:29:28,956 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:28,981 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SOL/USDT using full OHLCV data
2025-06-26 13:29:29,001 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:29,020 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SUI/USDT using full OHLCV data
2025-06-26 13:29:29,037 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:29,060 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/XRP/USDT using full OHLCV data
2025-06-26 13:29:29,082 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 13:29:29,082 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:29,099 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 13:29:29,105 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AAVE/USDT using full OHLCV data
2025-06-26 13:29:29,131 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:29,154 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AVAX/USDT using full OHLCV data
2025-06-26 13:29:29,177 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:29,201 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ADA/USDT using full OHLCV data
2025-06-26 13:29:29,221 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:29,240 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/TRX/USDT using full OHLCV data
2025-06-26 13:29:29,259 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 13:29:29,259 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:29,278 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 13:29:29,283 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/PEPE/USDT using full OHLCV data
2025-06-26 13:29:29,301 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:29,319 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOGE/USDT using full OHLCV data
2025-06-26 13:29:29,338 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:29,359 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BNB/USDT using full OHLCV data
2025-06-26 13:29:29,376 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:29,423 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOT/USDT using full OHLCV data
2025-06-26 13:29:29,443 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:29,465 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ETH/USDT using full OHLCV data
2025-06-26 13:29:29,481 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:29,501 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BTC/USDT using full OHLCV data
2025-06-26 13:29:29,517 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 13:29:29,518 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:29,533 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 13:29:29,538 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SOL/USDT using full OHLCV data
2025-06-26 13:29:29,557 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:29,577 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SUI/USDT using full OHLCV data
2025-06-26 13:29:29,598 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 13:29:29,598 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:29,616 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 13:29:29,620 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/XRP/USDT using full OHLCV data
2025-06-26 13:29:29,639 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 13:29:29,639 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:29,655 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 13:29:29,664 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AAVE/USDT using full OHLCV data
2025-06-26 13:29:29,680 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 13:29:29,680 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:29,695 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 13:29:29,701 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AVAX/USDT using full OHLCV data
2025-06-26 13:29:29,719 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:29,743 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ADA/USDT using full OHLCV data
2025-06-26 13:29:29,761 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 13:29:29,762 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:29,775 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 13:29:29,780 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/LINK/USDT using full OHLCV data
2025-06-26 13:29:29,796 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 13:29:29,797 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:29,812 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 13:29:29,817 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/PEPE/USDT using full OHLCV data
2025-06-26 13:29:29,832 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:29,852 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOGE/USDT using full OHLCV data
2025-06-26 13:29:29,871 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:29,893 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BNB/USDT using full OHLCV data
2025-06-26 13:29:29,914 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:29,935 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOT/USDT using full OHLCV data
2025-06-26 13:29:29,953 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:29,974 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ETH/USDT using full OHLCV data
2025-06-26 13:29:29,995 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:30,016 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BTC/USDT using full OHLCV data
2025-06-26 13:29:30,036 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:30,062 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SOL/USDT using full OHLCV data
2025-06-26 13:29:30,076 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:30,101 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SUI/USDT using full OHLCV data
2025-06-26 13:29:30,117 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:30,140 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/XRP/USDT using full OHLCV data
2025-06-26 13:29:30,159 - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-06-26 13:29:30,159 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:30,180 - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-06-26 13:29:30,183 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AAVE/USDT using full OHLCV data
2025-06-26 13:29:30,205 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 13:29:30,206 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:30,220 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 13:29:30,226 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AVAX/USDT using full OHLCV data
2025-06-26 13:29:30,243 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 13:29:30,243 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:30,257 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 13:29:30,263 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ADA/USDT using full OHLCV data
2025-06-26 13:29:30,277 - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-06-26 13:29:30,277 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:30,295 - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-06-26 13:29:30,298 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/LINK/USDT using full OHLCV data
2025-06-26 13:29:30,312 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:30,326 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/TRX/USDT using full OHLCV data
2025-06-26 13:29:30,345 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:30,369 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOGE/USDT using full OHLCV data
2025-06-26 13:29:30,376 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:30,401 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BNB/USDT using full OHLCV data
2025-06-26 13:29:30,415 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:30,431 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOT/USDT using full OHLCV data
2025-06-26 13:29:30,445 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:30,501 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ETH/USDT using full OHLCV data
2025-06-26 13:29:30,525 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:30,544 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BTC/USDT using full OHLCV data
2025-06-26 13:29:30,562 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:30,577 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SOL/USDT using full OHLCV data
2025-06-26 13:29:30,594 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 13:29:30,594 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:30,610 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 13:29:30,615 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SUI/USDT using full OHLCV data
2025-06-26 13:29:30,631 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 13:29:30,632 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:30,645 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 13:29:30,650 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/XRP/USDT using full OHLCV data
2025-06-26 13:29:30,668 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 13:29:30,669 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:30,683 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 13:29:30,688 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AAVE/USDT using full OHLCV data
2025-06-26 13:29:30,701 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 13:29:30,701 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:30,719 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 13:29:30,724 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AVAX/USDT using full OHLCV data
2025-06-26 13:29:30,739 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 13:29:30,740 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:30,751 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 13:29:30,757 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ADA/USDT using full OHLCV data
2025-06-26 13:29:30,766 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 13:29:30,774 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:30,789 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 13:29:30,794 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/LINK/USDT using full OHLCV data
2025-06-26 13:29:30,812 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 13:29:30,813 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:30,826 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 13:29:30,827 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/TRX/USDT using full OHLCV data
2025-06-26 13:29:30,842 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 13:29:30,842 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:30,860 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 13:29:30,862 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/PEPE/USDT using full OHLCV data
2025-06-26 13:29:30,877 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:30,892 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BNB/USDT using full OHLCV data
2025-06-26 13:29:30,914 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 13:29:30,914 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:30,929 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 13:29:30,933 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/DOT/USDT using full OHLCV data
2025-06-26 13:29:30,951 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:30,979 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ETH/USDT using full OHLCV data
2025-06-26 13:29:30,994 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:31,011 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/BTC/USDT using full OHLCV data
2025-06-26 13:29:31,027 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 13:29:31,030 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:31,042 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 13:29:31,047 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SOL/USDT using full OHLCV data
2025-06-26 13:29:31,063 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 13:29:31,063 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:31,078 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 13:29:31,080 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SUI/USDT using full OHLCV data
2025-06-26 13:29:31,101 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 13:29:31,101 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:31,112 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 13:29:31,122 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/XRP/USDT using full OHLCV data
2025-06-26 13:29:31,130 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 13:29:31,130 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:31,153 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 13:29:31,157 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AAVE/USDT using full OHLCV data
2025-06-26 13:29:31,175 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 13:29:31,175 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:31,184 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 13:29:31,199 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AVAX/USDT using full OHLCV data
2025-06-26 13:29:31,219 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 13:29:31,219 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:31,226 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 13:29:31,242 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ADA/USDT using full OHLCV data
2025-06-26 13:29:31,264 - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-06-26 13:29:31,264 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:31,282 - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-06-26 13:29:31,289 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/LINK/USDT using full OHLCV data
2025-06-26 13:29:31,306 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 13:29:31,306 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:31,322 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 13:29:31,327 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/TRX/USDT using full OHLCV data
2025-06-26 13:29:31,344 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 13:29:31,344 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:31,362 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 13:29:31,363 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/PEPE/USDT using full OHLCV data
2025-06-26 13:29:31,377 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:31,405 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOGE/USDT using full OHLCV data
2025-06-26 13:29:31,425 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:31,445 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOT/USDT using full OHLCV data
2025-06-26 13:29:31,463 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:31,478 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ETH/USDT using full OHLCV data
2025-06-26 13:29:31,493 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:31,511 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BTC/USDT using full OHLCV data
2025-06-26 13:29:31,526 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:31,551 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SOL/USDT using full OHLCV data
2025-06-26 13:29:31,566 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 13:29:31,566 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:31,577 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 13:29:31,588 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SUI/USDT using full OHLCV data
2025-06-26 13:29:31,604 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 13:29:31,605 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:31,617 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 13:29:31,619 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/XRP/USDT using full OHLCV data
2025-06-26 13:29:31,631 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 13:29:31,631 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:31,651 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 13:29:31,659 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AAVE/USDT using full OHLCV data
2025-06-26 13:29:31,676 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 13:29:31,676 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:31,684 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 13:29:31,696 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AVAX/USDT using full OHLCV data
2025-06-26 13:29:31,713 - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-06-26 13:29:31,714 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:31,729 - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-06-26 13:29:31,730 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ADA/USDT using full OHLCV data
2025-06-26 13:29:31,750 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 13:29:31,750 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:31,764 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 13:29:31,764 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/LINK/USDT using full OHLCV data
2025-06-26 13:29:31,777 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:31,805 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/TRX/USDT using full OHLCV data
2025-06-26 13:29:31,822 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 13:29:31,822 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:31,827 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 13:29:31,842 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/PEPE/USDT using full OHLCV data
2025-06-26 13:29:31,860 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:31,882 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/DOGE/USDT using full OHLCV data
2025-06-26 13:29:31,898 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 13:29:31,915 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BNB/USDT using full OHLCV data
2025-06-26 13:29:33,731 - root - INFO - Successfully calculated daily scores using ratio-based comparisons.
2025-06-26 13:29:33,731 - root - INFO - Finished calculating daily scores. DataFrame shape: (196, 14)
2025-06-26 13:29:33,731 - root - WARNING - Running strategy with n_assets=1, equal allocation, MTPI filtering DISABLED
2025-06-26 13:29:33,731 - root - INFO - Date ranges for each asset:
2025-06-26 13:29:33,731 - root - INFO -   ETH/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 13:29:33,731 - root - INFO -   BTC/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 13:29:33,731 - root - INFO -   SOL/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 13:29:33,731 - root - INFO -   SUI/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 13:29:33,731 - root - INFO -   XRP/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 13:29:33,731 - root - INFO -   AAVE/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 13:29:33,739 - root - INFO -   AVAX/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 13:29:33,740 - root - INFO -   ADA/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 13:29:33,740 - root - INFO -   LINK/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 13:29:33,741 - root - INFO -   TRX/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 13:29:33,741 - root - INFO -   PEPE/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 13:29:33,741 - root - INFO -   DOGE/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 13:29:33,741 - root - INFO -   BNB/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 13:29:33,742 - root - INFO -   DOT/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 13:29:33,742 - root - INFO - Common dates range: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 13:29:33,743 - root - INFO - Analysis will run from: 2025-02-10 to 2025-06-25 (136 candles)
2025-06-26 13:29:33,745 - root - INFO - EXECUTION TIMING VERIFICATION:
2025-06-26 13:29:33,745 - root - INFO -    Execution Method: candle_close
2025-06-26 13:29:33,745 - root - INFO -    Automatic execution at 00:00 UTC (candle close)
2025-06-26 13:29:33,745 - root - INFO -    Signal generated and executed immediately
2025-06-26 13:29:33,751 - root - INFO - Including ETH/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,751 - root - INFO - Including BTC/USDT on 2025-02-10 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,751 - root - INFO - Including SOL/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,751 - root - INFO - Including SUI/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,752 - root - INFO - Including XRP/USDT on 2025-02-10 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,752 - root - INFO - Including AAVE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,752 - root - INFO - Including AVAX/USDT on 2025-02-10 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,752 - root - INFO - Including ADA/USDT on 2025-02-10 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,752 - root - INFO - Including LINK/USDT on 2025-02-10 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,752 - root - INFO - Including TRX/USDT on 2025-02-10 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,752 - root - INFO - Including PEPE/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,752 - root - INFO - Including DOGE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,752 - root - INFO - Including BNB/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,752 - root - INFO - Including DOT/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,752 - root - INFO - ASSET CHANGE DETECTED on 2025-02-11:
2025-06-26 13:29:33,752 - root - INFO -    Signal Date: 2025-02-10 (generated at 00:00 UTC)
2025-06-26 13:29:33,752 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-11 00:00 UTC (immediate)
2025-06-26 13:29:33,752 - root - INFO -    Execution Delay: 0 hours
2025-06-26 13:29:33,752 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 13:29:33,752 - root - INFO -    TRX/USDT buy price: $0.2410 (close price)
2025-06-26 13:29:33,752 - root - INFO - Including ETH/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,752 - root - INFO - Including BTC/USDT on 2025-02-11 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,752 - root - INFO - Including SOL/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,756 - root - INFO - Including SUI/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,756 - root - INFO - Including XRP/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,756 - root - INFO - Including AAVE/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,756 - root - INFO - Including AVAX/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,756 - root - INFO - Including ADA/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,758 - root - INFO - Including LINK/USDT on 2025-02-11 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,758 - root - INFO - Including TRX/USDT on 2025-02-11 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,758 - root - INFO - Including PEPE/USDT on 2025-02-11 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,758 - root - INFO - Including DOGE/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,758 - root - INFO - Including BNB/USDT on 2025-02-11 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,758 - root - INFO - Including DOT/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,759 - root - INFO - Including ETH/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,759 - root - INFO - Including BTC/USDT on 2025-02-12 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,759 - root - INFO - Including SOL/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,759 - root - INFO - Including SUI/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,759 - root - INFO - Including XRP/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,759 - root - INFO - Including AAVE/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,759 - root - INFO - Including AVAX/USDT on 2025-02-12 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,759 - root - INFO - Including ADA/USDT on 2025-02-12 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,759 - root - INFO - Including LINK/USDT on 2025-02-12 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,759 - root - INFO - Including TRX/USDT on 2025-02-12 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,761 - root - INFO - Including PEPE/USDT on 2025-02-12 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,761 - root - INFO - Including DOGE/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,762 - root - INFO - Including BNB/USDT on 2025-02-12 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,762 - root - INFO - Including DOT/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,762 - root - INFO - ASSET CHANGE DETECTED on 2025-02-13:
2025-06-26 13:29:33,762 - root - INFO -    Signal Date: 2025-02-12 (generated at 00:00 UTC)
2025-06-26 13:29:33,762 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-13 00:00 UTC (immediate)
2025-06-26 13:29:33,762 - root - INFO -    Execution Delay: 0 hours
2025-06-26 13:29:33,762 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 13:29:33,762 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 13:29:33,763 - root - INFO -    BNB/USDT buy price: $664.7200 (close price)
2025-06-26 13:29:33,763 - root - INFO - Including ETH/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,763 - root - INFO - Including BTC/USDT on 2025-02-13 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,763 - root - INFO - Including SOL/USDT on 2025-02-13 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,763 - root - INFO - Including SUI/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,763 - root - INFO - Including XRP/USDT on 2025-02-13 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,764 - root - INFO - Including AAVE/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,764 - root - INFO - Including AVAX/USDT on 2025-02-13 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,764 - root - INFO - Including ADA/USDT on 2025-02-13 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,764 - root - INFO - Including LINK/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,764 - root - INFO - Including TRX/USDT on 2025-02-13 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,764 - root - INFO - Including PEPE/USDT on 2025-02-13 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,764 - root - INFO - Including DOGE/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,764 - root - INFO - Including BNB/USDT on 2025-02-13 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,764 - root - INFO - Including DOT/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,765 - root - INFO - Including ETH/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,765 - root - INFO - Including BTC/USDT on 2025-02-14 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,765 - root - INFO - Including SOL/USDT on 2025-02-14 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,765 - root - INFO - Including SUI/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,765 - root - INFO - Including XRP/USDT on 2025-02-14 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,765 - root - INFO - Including AAVE/USDT on 2025-02-14 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,765 - root - INFO - Including AVAX/USDT on 2025-02-14 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,765 - root - INFO - Including ADA/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,765 - root - INFO - Including LINK/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,765 - root - INFO - Including TRX/USDT on 2025-02-14 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,765 - root - INFO - Including PEPE/USDT on 2025-02-14 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,765 - root - INFO - Including DOGE/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,765 - root - INFO - Including BNB/USDT on 2025-02-14 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,766 - root - INFO - Including DOT/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 13:29:33,766 - root - INFO - ASSET CHANGE DETECTED on 2025-02-19:
2025-06-26 13:29:33,766 - root - INFO -    Signal Date: 2025-02-18 (generated at 00:00 UTC)
2025-06-26 13:29:33,766 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-19 00:00 UTC (immediate)
2025-06-26 13:29:33,766 - root - INFO -    Execution Delay: 0 hours
2025-06-26 13:29:33,769 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 13:29:33,769 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 13:29:33,769 - root - INFO -    TRX/USDT buy price: $0.2424 (close price)
2025-06-26 13:29:33,771 - root - INFO - ASSET CHANGE DETECTED on 2025-02-23:
2025-06-26 13:29:33,771 - root - INFO -    Signal Date: 2025-02-22 (generated at 00:00 UTC)
2025-06-26 13:29:33,772 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-23 00:00 UTC (immediate)
2025-06-26 13:29:33,772 - root - INFO -    Execution Delay: 0 hours
2025-06-26 13:29:33,772 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 13:29:33,772 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 13:29:33,772 - root - INFO -    BNB/USDT buy price: $658.4200 (close price)
2025-06-26 13:29:33,773 - root - INFO - ASSET CHANGE DETECTED on 2025-02-24:
2025-06-26 13:29:33,773 - root - INFO -    Signal Date: 2025-02-23 (generated at 00:00 UTC)
2025-06-26 13:29:33,773 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-24 00:00 UTC (immediate)
2025-06-26 13:29:33,773 - root - INFO -    Execution Delay: 0 hours
2025-06-26 13:29:33,773 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 13:29:33,774 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 13:29:33,774 - root - INFO -    TRX/USDT buy price: $0.2401 (close price)
2025-06-26 13:29:33,774 - root - INFO - ASSET CHANGE DETECTED on 2025-03-03:
2025-06-26 13:29:33,774 - root - INFO -    Signal Date: 2025-03-02 (generated at 00:00 UTC)
2025-06-26 13:29:33,774 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-03 00:00 UTC (immediate)
2025-06-26 13:29:33,774 - root - INFO -    Execution Delay: 0 hours
2025-06-26 13:29:33,774 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 13:29:33,778 - root - INFO -    Buying: ['ADA/USDT']
2025-06-26 13:29:33,778 - root - INFO -    ADA/USDT buy price: $0.8578 (close price)
2025-06-26 13:29:33,780 - root - INFO - ASSET CHANGE DETECTED on 2025-03-11:
2025-06-26 13:29:33,781 - root - INFO -    Signal Date: 2025-03-10 (generated at 00:00 UTC)
2025-06-26 13:29:33,781 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-11 00:00 UTC (immediate)
2025-06-26 13:29:33,782 - root - INFO -    Execution Delay: 0 hours
2025-06-26 13:29:33,782 - root - INFO -    Selling: ['ADA/USDT']
2025-06-26 13:29:33,782 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 13:29:33,782 - root - INFO -    TRX/USDT buy price: $0.2244 (close price)
2025-06-26 13:29:33,783 - root - INFO - ASSET CHANGE DETECTED on 2025-03-15:
2025-06-26 13:29:33,784 - root - INFO -    Signal Date: 2025-03-14 (generated at 00:00 UTC)
2025-06-26 13:29:33,784 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-15 00:00 UTC (immediate)
2025-06-26 13:29:33,784 - root - INFO -    Execution Delay: 0 hours
2025-06-26 13:29:33,784 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 13:29:33,784 - root - INFO -    Buying: ['ADA/USDT']
2025-06-26 13:29:33,784 - root - INFO -    ADA/USDT buy price: $0.7468 (close price)
2025-06-26 13:29:33,784 - root - INFO - ASSET CHANGE DETECTED on 2025-03-17:
2025-06-26 13:29:33,784 - root - INFO -    Signal Date: 2025-03-16 (generated at 00:00 UTC)
2025-06-26 13:29:33,784 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-17 00:00 UTC (immediate)
2025-06-26 13:29:33,784 - root - INFO -    Execution Delay: 0 hours
2025-06-26 13:29:33,784 - root - INFO -    Selling: ['ADA/USDT']
2025-06-26 13:29:33,787 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 13:29:33,787 - root - INFO -    BNB/USDT buy price: $631.6900 (close price)
2025-06-26 13:29:33,788 - root - INFO - ASSET CHANGE DETECTED on 2025-03-20:
2025-06-26 13:29:33,788 - root - INFO -    Signal Date: 2025-03-19 (generated at 00:00 UTC)
2025-06-26 13:29:33,788 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-20 00:00 UTC (immediate)
2025-06-26 13:29:33,788 - root - INFO -    Execution Delay: 0 hours
2025-06-26 13:29:33,788 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 13:29:33,788 - root - INFO -    Buying: ['XRP/USDT']
2025-06-26 13:29:33,788 - root - INFO -    XRP/USDT buy price: $2.4361 (close price)
2025-06-26 13:29:33,789 - root - INFO - ASSET CHANGE DETECTED on 2025-03-22:
2025-06-26 13:29:33,789 - root - INFO -    Signal Date: 2025-03-21 (generated at 00:00 UTC)
2025-06-26 13:29:33,789 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-22 00:00 UTC (immediate)
2025-06-26 13:29:33,790 - root - INFO -    Execution Delay: 0 hours
2025-06-26 13:29:33,790 - root - INFO -    Selling: ['XRP/USDT']
2025-06-26 13:29:33,790 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 13:29:33,790 - root - INFO -    BNB/USDT buy price: $627.0100 (close price)
2025-06-26 13:29:33,791 - root - INFO - ASSET CHANGE DETECTED on 2025-03-26:
2025-06-26 13:29:33,791 - root - INFO -    Signal Date: 2025-03-25 (generated at 00:00 UTC)
2025-06-26 13:29:33,791 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-26 00:00 UTC (immediate)
2025-06-26 13:29:33,791 - root - INFO -    Execution Delay: 0 hours
2025-06-26 13:29:33,791 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 13:29:33,791 - root - INFO -    Buying: ['AVAX/USDT']
2025-06-26 13:29:33,791 - root - INFO -    AVAX/USDT buy price: $22.0400 (close price)
2025-06-26 13:29:33,791 - root - INFO - ASSET CHANGE DETECTED on 2025-03-27:
2025-06-26 13:29:33,791 - root - INFO -    Signal Date: 2025-03-26 (generated at 00:00 UTC)
2025-06-26 13:29:33,791 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-27 00:00 UTC (immediate)
2025-06-26 13:29:33,791 - root - INFO -    Execution Delay: 0 hours
2025-06-26 13:29:33,791 - root - INFO -    Selling: ['AVAX/USDT']
2025-06-26 13:29:33,791 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 13:29:33,796 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 13:29:33,796 - root - INFO - ASSET CHANGE DETECTED on 2025-03-31:
2025-06-26 13:29:33,796 - root - INFO -    Signal Date: 2025-03-30 (generated at 00:00 UTC)
2025-06-26 13:29:33,796 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-31 00:00 UTC (immediate)
2025-06-26 13:29:33,796 - root - INFO -    Execution Delay: 0 hours
2025-06-26 13:29:33,798 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 13:29:33,798 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 13:29:33,798 - root - INFO -    BNB/USDT buy price: $604.8100 (close price)
2025-06-26 13:29:33,798 - root - INFO - ASSET CHANGE DETECTED on 2025-04-01:
2025-06-26 13:29:33,798 - root - INFO -    Signal Date: 2025-03-31 (generated at 00:00 UTC)
2025-06-26 13:29:33,799 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-01 00:00 UTC (immediate)
2025-06-26 13:29:33,799 - root - INFO -    Execution Delay: 0 hours
2025-06-26 13:29:33,799 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 13:29:33,799 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 13:29:33,799 - root - INFO -    TRX/USDT buy price: $0.2378 (close price)
2025-06-26 13:29:33,801 - root - INFO - ASSET CHANGE DETECTED on 2025-04-19:
2025-06-26 13:29:33,801 - root - INFO -    Signal Date: 2025-04-18 (generated at 00:00 UTC)
2025-06-26 13:29:33,801 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-19 00:00 UTC (immediate)
2025-06-26 13:29:33,801 - root - INFO -    Execution Delay: 0 hours
2025-06-26 13:29:33,805 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 13:29:33,805 - root - INFO -    Buying: ['SOL/USDT']
2025-06-26 13:29:33,805 - root - INFO -    SOL/USDT buy price: $139.8700 (close price)
2025-06-26 13:29:33,806 - root - INFO - ASSET CHANGE DETECTED on 2025-04-24:
2025-06-26 13:29:33,807 - root - INFO -    Signal Date: 2025-04-23 (generated at 00:00 UTC)
2025-06-26 13:29:33,807 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-24 00:00 UTC (immediate)
2025-06-26 13:29:33,807 - root - INFO -    Execution Delay: 0 hours
2025-06-26 13:29:33,807 - root - INFO -    Selling: ['SOL/USDT']
2025-06-26 13:29:33,807 - root - INFO -    Buying: ['SUI/USDT']
2025-06-26 13:29:33,807 - root - INFO -    SUI/USDT buy price: $3.3437 (close price)
2025-06-26 13:29:33,815 - root - INFO - ASSET CHANGE DETECTED on 2025-05-10:
2025-06-26 13:29:33,816 - root - INFO -    Signal Date: 2025-05-09 (generated at 00:00 UTC)
2025-06-26 13:29:33,816 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-10 00:00 UTC (immediate)
2025-06-26 13:29:33,816 - root - INFO -    Execution Delay: 0 hours
2025-06-26 13:29:33,816 - root - INFO -    Selling: ['SUI/USDT']
2025-06-26 13:29:33,817 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 13:29:33,817 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 13:29:33,818 - root - INFO - ASSET CHANGE DETECTED on 2025-05-21:
2025-06-26 13:29:33,818 - root - INFO -    Signal Date: 2025-05-20 (generated at 00:00 UTC)
2025-06-26 13:29:33,818 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-21 00:00 UTC (immediate)
2025-06-26 13:29:33,818 - root - INFO -    Execution Delay: 0 hours
2025-06-26 13:29:33,822 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 13:29:33,822 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-26 13:29:33,822 - root - INFO -    AAVE/USDT buy price: $247.5400 (close price)
2025-06-26 13:29:33,823 - root - INFO - ASSET CHANGE DETECTED on 2025-05-23:
2025-06-26 13:29:33,823 - root - INFO -    Signal Date: 2025-05-22 (generated at 00:00 UTC)
2025-06-26 13:29:33,823 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-23 00:00 UTC (immediate)
2025-06-26 13:29:33,823 - root - INFO -    Execution Delay: 0 hours
2025-06-26 13:29:33,824 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-26 13:29:33,824 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 13:29:33,824 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 13:29:33,824 - root - INFO - ASSET CHANGE DETECTED on 2025-05-25:
2025-06-26 13:29:33,824 - root - INFO -    Signal Date: 2025-05-24 (generated at 00:00 UTC)
2025-06-26 13:29:33,824 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-25 00:00 UTC (immediate)
2025-06-26 13:29:33,824 - root - INFO -    Execution Delay: 0 hours
2025-06-26 13:29:33,824 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 13:29:33,824 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-26 13:29:33,824 - root - INFO -    AAVE/USDT buy price: $269.2800 (close price)
2025-06-26 13:29:33,835 - root - INFO - ASSET CHANGE DETECTED on 2025-06-21:
2025-06-26 13:29:33,835 - root - INFO -    Signal Date: 2025-06-20 (generated at 00:00 UTC)
2025-06-26 13:29:33,835 - root - INFO -    AUTOMATIC EXECUTION: 2025-06-21 00:00 UTC (immediate)
2025-06-26 13:29:33,835 - root - INFO -    Execution Delay: 0 hours
2025-06-26 13:29:33,838 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-26 13:29:33,838 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 13:29:33,838 - root - INFO -    TRX/USDT buy price: $0.2710 (close price)
2025-06-26 13:29:33,867 - root - INFO - Entry trade at 2025-02-11 00:00:00+00:00: TRX/USDT
2025-06-26 13:29:33,867 - root - INFO - Swap trade at 2025-02-13 00:00:00+00:00: TRX/USDT -> BNB/USDT
2025-06-26 13:29:33,867 - root - INFO - Swap trade at 2025-02-19 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-26 13:29:33,867 - root - INFO - Swap trade at 2025-02-23 00:00:00+00:00: TRX/USDT -> BNB/USDT
2025-06-26 13:29:33,867 - root - INFO - Swap trade at 2025-02-24 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-26 13:29:33,867 - root - INFO - Swap trade at 2025-03-03 00:00:00+00:00: TRX/USDT -> ADA/USDT
2025-06-26 13:29:33,867 - root - INFO - Swap trade at 2025-03-11 00:00:00+00:00: ADA/USDT -> TRX/USDT
2025-06-26 13:29:33,867 - root - INFO - Swap trade at 2025-03-15 00:00:00+00:00: TRX/USDT -> ADA/USDT
2025-06-26 13:29:33,867 - root - INFO - Swap trade at 2025-03-17 00:00:00+00:00: ADA/USDT -> BNB/USDT
2025-06-26 13:29:33,874 - root - INFO - Swap trade at 2025-03-20 00:00:00+00:00: BNB/USDT -> XRP/USDT
2025-06-26 13:29:33,874 - root - INFO - Swap trade at 2025-03-22 00:00:00+00:00: XRP/USDT -> BNB/USDT
2025-06-26 13:29:33,874 - root - INFO - Swap trade at 2025-03-26 00:00:00+00:00: BNB/USDT -> AVAX/USDT
2025-06-26 13:29:33,874 - root - INFO - Swap trade at 2025-03-27 00:00:00+00:00: AVAX/USDT -> PEPE/USDT
2025-06-26 13:29:33,874 - root - INFO - Swap trade at 2025-03-31 00:00:00+00:00: PEPE/USDT -> BNB/USDT
2025-06-26 13:29:33,874 - root - INFO - Swap trade at 2025-04-01 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-26 13:29:33,874 - root - INFO - Swap trade at 2025-04-19 00:00:00+00:00: TRX/USDT -> SOL/USDT
2025-06-26 13:29:33,874 - root - INFO - Swap trade at 2025-04-24 00:00:00+00:00: SOL/USDT -> SUI/USDT
2025-06-26 13:29:33,874 - root - INFO - Swap trade at 2025-05-10 00:00:00+00:00: SUI/USDT -> PEPE/USDT
2025-06-26 13:29:33,874 - root - INFO - Swap trade at 2025-05-21 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-26 13:29:33,874 - root - INFO - Swap trade at 2025-05-23 00:00:00+00:00: AAVE/USDT -> PEPE/USDT
2025-06-26 13:29:33,874 - root - INFO - Swap trade at 2025-05-25 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-26 13:29:33,878 - root - INFO - Swap trade at 2025-06-21 00:00:00+00:00: AAVE/USDT -> TRX/USDT
2025-06-26 13:29:33,878 - root - INFO - Total trades: 22 (Entries: 1, Exits: 0, Swaps: 21)
2025-06-26 13:29:33,880 - root - INFO - Strategy execution completed in 0s
2025-06-26 13:29:33,880 - root - INFO - DEBUG: self.elapsed_time = 0.1492938995361328 seconds
2025-06-26 13:29:33,882 - root - INFO - Strategy equity curve starts at: 2025-02-10
2025-06-26 13:29:33,883 - root - INFO - Assets included in buy-and-hold comparison:
2025-06-26 13:29:33,883 - root - INFO -   ETH/USDT: First available date 2024-12-12
2025-06-26 13:29:33,883 - root - INFO -   BTC/USDT: First available date 2024-12-12
2025-06-26 13:29:33,883 - root - INFO -   SOL/USDT: First available date 2024-12-12
2025-06-26 13:29:33,884 - root - INFO -   SUI/USDT: First available date 2024-12-12
2025-06-26 13:29:33,884 - root - INFO -   XRP/USDT: First available date 2024-12-12
2025-06-26 13:29:33,884 - root - INFO -   AAVE/USDT: First available date 2024-12-12
2025-06-26 13:29:33,884 - root - INFO -   AVAX/USDT: First available date 2024-12-12
2025-06-26 13:29:33,884 - root - INFO -   ADA/USDT: First available date 2024-12-12
2025-06-26 13:29:33,884 - root - INFO -   LINK/USDT: First available date 2024-12-12
2025-06-26 13:29:33,884 - root - INFO -   TRX/USDT: First available date 2024-12-12
2025-06-26 13:29:33,884 - root - INFO -   PEPE/USDT: First available date 2024-12-12
2025-06-26 13:29:33,884 - root - INFO -   DOGE/USDT: First available date 2024-12-12
2025-06-26 13:29:33,885 - root - INFO -   BNB/USDT: First available date 2024-12-12
2025-06-26 13:29:33,885 - root - INFO -   DOT/USDT: First available date 2024-12-12
2025-06-26 13:29:33,885 - root - INFO - Using provided start date for normalization: 2025-02-10 00:00:00+00:00
2025-06-26 13:29:33,886 - root - INFO - Normalized ETH/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 13:29:33,887 - root - INFO - Normalized BTC/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 13:29:33,888 - root - INFO - Normalized SOL/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 13:29:33,888 - root - INFO - Normalized SUI/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 13:29:33,888 - root - INFO - Normalized XRP/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 13:29:33,891 - root - INFO - Normalized AAVE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 13:29:33,891 - root - INFO - Normalized AVAX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 13:29:33,895 - root - INFO - Normalized ADA/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 13:29:33,896 - root - INFO - Normalized LINK/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 13:29:33,897 - root - INFO - Normalized TRX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 13:29:33,898 - root - INFO - Normalized PEPE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 13:29:33,899 - root - INFO - Normalized DOGE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 13:29:33,901 - root - INFO - Normalized BNB/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 13:29:33,901 - root - INFO - Normalized DOT/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 13:29:33,902 - root - INFO - Added equity_curve to bnh_metrics for ETH/USDT with 196 points
2025-06-26 13:29:33,903 - root - INFO - ETH/USDT B&H total return: -9.12%
2025-06-26 13:29:33,904 - root - INFO - Added equity_curve to bnh_metrics for BTC/USDT with 196 points
2025-06-26 13:29:33,905 - root - INFO - BTC/USDT B&H total return: 10.17%
2025-06-26 13:29:33,906 - root - INFO - Added equity_curve to bnh_metrics for SOL/USDT with 196 points
2025-06-26 13:29:33,907 - root - INFO - SOL/USDT B&H total return: -28.38%
2025-06-26 13:29:33,908 - root - INFO - Added equity_curve to bnh_metrics for SUI/USDT with 196 points
2025-06-26 13:29:33,909 - root - INFO - SUI/USDT B&H total return: -15.06%
2025-06-26 13:29:33,911 - root - INFO - Added equity_curve to bnh_metrics for XRP/USDT with 196 points
2025-06-26 13:29:33,911 - root - INFO - XRP/USDT B&H total return: -9.81%
2025-06-26 13:29:33,913 - root - INFO - Added equity_curve to bnh_metrics for AAVE/USDT with 196 points
2025-06-26 13:29:33,913 - root - INFO - AAVE/USDT B&H total return: 1.32%
2025-06-26 13:29:33,914 - root - INFO - Added equity_curve to bnh_metrics for AVAX/USDT with 196 points
2025-06-26 13:29:33,915 - root - INFO - AVAX/USDT B&H total return: -31.55%
2025-06-26 13:29:33,916 - root - INFO - Added equity_curve to bnh_metrics for ADA/USDT with 196 points
2025-06-26 13:29:33,917 - root - INFO - ADA/USDT B&H total return: -20.36%
2025-06-26 13:29:33,919 - root - INFO - Added equity_curve to bnh_metrics for LINK/USDT with 196 points
2025-06-26 13:29:33,919 - root - INFO - LINK/USDT B&H total return: -30.20%
2025-06-26 13:29:33,920 - root - INFO - Added equity_curve to bnh_metrics for TRX/USDT with 196 points
2025-06-26 13:29:33,921 - root - INFO - TRX/USDT B&H total return: 10.93%
2025-06-26 13:29:33,922 - root - INFO - Added equity_curve to bnh_metrics for PEPE/USDT with 196 points
2025-06-26 13:29:33,922 - root - INFO - PEPE/USDT B&H total return: -1.36%
2025-06-26 13:29:33,924 - root - INFO - Added equity_curve to bnh_metrics for DOGE/USDT with 196 points
2025-06-26 13:29:33,924 - root - INFO - DOGE/USDT B&H total return: -35.56%
2025-06-26 13:29:33,926 - root - INFO - Added equity_curve to bnh_metrics for BNB/USDT with 196 points
2025-06-26 13:29:33,926 - root - INFO - BNB/USDT B&H total return: 4.43%
2025-06-26 13:29:33,929 - root - INFO - Added equity_curve to bnh_metrics for DOT/USDT with 196 points
2025-06-26 13:29:33,929 - root - INFO - DOT/USDT B&H total return: -30.82%
2025-06-26 13:29:33,931 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 13:29:33,935 - root - INFO - Configuration loaded successfully.
2025-06-26 13:29:33,946 - root - INFO - Using colored segments for single-asset strategy visualization
2025-06-26 13:29:34,034 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 13:29:34,040 - root - INFO - Configuration loaded successfully.
2025-06-26 13:29:35,326 - root - INFO - Added ETH/USDT buy-and-hold curve with 196 points
2025-06-26 13:29:35,326 - root - INFO - Added BTC/USDT buy-and-hold curve with 196 points
2025-06-26 13:29:35,326 - root - INFO - Added SOL/USDT buy-and-hold curve with 196 points
2025-06-26 13:29:35,326 - root - INFO - Added SUI/USDT buy-and-hold curve with 196 points
2025-06-26 13:29:35,326 - root - INFO - Added XRP/USDT buy-and-hold curve with 196 points
2025-06-26 13:29:35,326 - root - INFO - Added AAVE/USDT buy-and-hold curve with 196 points
2025-06-26 13:29:35,326 - root - INFO - Added AVAX/USDT buy-and-hold curve with 196 points
2025-06-26 13:29:35,326 - root - INFO - Added ADA/USDT buy-and-hold curve with 196 points
2025-06-26 13:29:35,326 - root - INFO - Added LINK/USDT buy-and-hold curve with 196 points
2025-06-26 13:29:35,326 - root - INFO - Added TRX/USDT buy-and-hold curve with 196 points
2025-06-26 13:29:35,326 - root - INFO - Added PEPE/USDT buy-and-hold curve with 196 points
2025-06-26 13:29:35,326 - root - INFO - Added DOGE/USDT buy-and-hold curve with 196 points
2025-06-26 13:29:35,326 - root - INFO - Added BNB/USDT buy-and-hold curve with 196 points
2025-06-26 13:29:35,326 - root - INFO - Added DOT/USDT buy-and-hold curve with 196 points
2025-06-26 13:29:35,326 - root - INFO - Added 14 buy-and-hold curves to results
2025-06-26 13:29:35,326 - root - INFO -   - ETH/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 13:29:35,326 - root - INFO -   - BTC/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 13:29:35,326 - root - INFO -   - SOL/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 13:29:35,326 - root - INFO -   - SUI/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 13:29:35,326 - root - INFO -   - XRP/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 13:29:35,326 - root - INFO -   - AAVE/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 13:29:35,326 - root - INFO -   - AVAX/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 13:29:35,326 - root - INFO -   - ADA/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 13:29:35,326 - root - INFO -   - LINK/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 13:29:35,326 - root - INFO -   - TRX/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 13:29:35,326 - root - INFO -   - PEPE/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 13:29:35,326 - root - INFO -   - DOGE/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 13:29:35,333 - root - INFO -   - BNB/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 13:29:35,333 - root - INFO -   - DOT/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 13:29:35,342 - root - INFO - Converting trend detection results from USDT to EUR pairs for trading
2025-06-26 13:29:35,342 - root - INFO - Asset conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-06-26 13:29:35,346 - root - INFO - Successfully converted best asset series from USDT to EUR pairs
2025-06-26 13:29:35,347 - root - INFO - MTPI disabled - skipping MTPI signal calculation
2025-06-26 13:29:35,349 - root - INFO - Successfully converted assets_held_df from USDT to EUR pairs
2025-06-26 13:29:35,349 - root - INFO - Latest scores extracted (USDT): {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-06-26 13:29:35,429 - root - INFO - Latest scores converted to EUR: {'ETH/EUR': 8.0, 'BTC/EUR': 12.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 3.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-26 13:29:35,442 - root - INFO - Saved metrics to new file: Performance_Metrics\metrics_BestAsset_1d_1d_assets14_since_20250619_run_********_132916.csv
2025-06-26 13:29:35,442 - root - INFO - Saved performance metrics to CSV: Performance_Metrics\metrics_BestAsset_1d_1d_assets14_since_20250619_run_********_132916.csv
2025-06-26 13:29:35,442 - root - INFO - === RESULTS STRUCTURE DEBUGGING ===
2025-06-26 13:29:35,443 - root - INFO - Results type: <class 'dict'>
2025-06-26 13:29:35,443 - root - INFO - Results keys: dict_keys(['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message'])
2025-06-26 13:29:35,443 - root - INFO - Success flag set to: True
2025-06-26 13:29:35,443 - root - INFO - Message set to: Strategy calculation completed successfully
2025-06-26 13:29:35,444 - root - INFO -   - strategy_equity: <class 'pandas.core.series.Series'> with 196 entries
2025-06-26 13:29:35,444 - root - INFO -   - buy_hold_curves: dict with 14 entries
2025-06-26 13:29:35,444 - root - INFO -   - best_asset_series: <class 'pandas.core.series.Series'> with 196 entries
2025-06-26 13:29:35,444 - root - INFO -   - mtpi_signals: <class 'NoneType'>
2025-06-26 13:29:35,445 - root - INFO -   - mtpi_score: <class 'NoneType'>
2025-06-26 13:29:35,445 - root - INFO -   - assets_held_df: <class 'pandas.core.frame.DataFrame'> with 196 entries
2025-06-26 13:29:35,445 - root - INFO -   - performance_metrics: dict with 3 entries
2025-06-26 13:29:35,445 - root - INFO -   - metrics_file: <class 'str'>
2025-06-26 13:29:35,446 - root - INFO -   - mtpi_filtering_metadata: dict with 2 entries
2025-06-26 13:29:35,446 - root - INFO -   - success: <class 'bool'>
2025-06-26 13:29:35,446 - root - INFO -   - message: <class 'str'>
2025-06-26 13:29:35,446 - root - INFO - MTPI disabled - using default bullish signal (1) for trading execution
2025-06-26 13:29:35,447 - root - INFO - ASSET SELECTION DEBUG - Extracted best_asset from best_asset_series: TRX/EUR
2025-06-26 13:29:35,448 - root - INFO - ASSET SELECTION DEBUG - Last 5 entries in best_asset_series: 2025-06-21 00:00:00+00:00    TRX/EUR
2025-06-22 00:00:00+00:00    TRX/EUR
2025-06-23 00:00:00+00:00    TRX/EUR
2025-06-24 00:00:00+00:00    TRX/EUR
2025-06-25 00:00:00+00:00    TRX/EUR
dtype: object
2025-06-26 13:29:35,472 - root - WARNING - No 'assets_held' column found in assets_held_df. Columns: Index(['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR',
       'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR',
       'BNB/EUR', 'DOT/EUR'],
      dtype='object')
2025-06-26 13:29:35,472 - root - INFO - Last row columns: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 13:29:35,472 - root - INFO - Single asset strategy with best asset: TRX/EUR
2025-06-26 13:29:35,472 - root - INFO - Executing single-asset strategy with best asset: TRX/EUR
2025-06-26 13:29:35,472 - root - INFO - Executing strategy signal: best_asset=TRX/EUR, mtpi_signal=1, mode=paper
2025-06-26 13:29:35,473 - root - INFO - Incremented daily trade counter for TRX/EUR: 1/5
2025-06-26 13:29:35,473 - root - INFO - ASSET SELECTION - Attempting to enter position for selected asset: TRX/EUR
2025-06-26 13:29:35,474 - root - INFO - Attempting to enter position for TRX/EUR in paper mode
2025-06-26 13:29:35,474 - root - INFO - TRADE ATTEMPT - TRX/EUR: Getting current market price...
2025-06-26 13:29:35,769 - root - INFO - TRADE ATTEMPT - TRX/EUR: Current price: 0.********
2025-06-26 13:29:35,771 - root - INFO - Available balance for EUR: 100.********
2025-06-26 13:29:35,771 - root - INFO - Loaded market info for 176 trading pairs
2025-06-26 13:29:35,777 - root - INFO - Calculated position size for TRX/EUR: 42.******** (using 10% of 100, accounting for fees)
2025-06-26 13:29:35,777 - root - INFO - Calculated position size: 42.******** TRX
2025-06-26 13:29:35,777 - root - INFO - Entering position for TRX/EUR: 42.******** units at 0.******** (value: 9.******** EUR)
2025-06-26 13:29:35,777 - root - INFO - Executing paper market buy order for TRX/EUR
2025-06-26 13:29:35,779 - root - INFO - Paper trading buy for TRX/EUR: original=42.********, adjusted=42.********, after_fee=42.********
2025-06-26 13:29:35,780 - root - INFO - Saved paper trading history to paper_trading_history.json
2025-06-26 13:29:35,780 - root - INFO - Created paper market buy order: TRX/EUR, amount: 42.********207215, price: 0.23285
2025-06-26 13:29:35,780 - root - INFO - Filled amount: 42.******** TRX
2025-06-26 13:29:35,780 - root - INFO - Order fee: 0.******** EUR
2025-06-26 13:29:35,780 - root - INFO - Successfully entered position: TRX/EUR, amount: 42.********, price: 0.********
2025-06-26 13:29:35,785 - root - INFO - Trade executed: BUY TRX/EUR, amount=42.********, price=0.********, filled=42.********
2025-06-26 13:29:35,785 - root - INFO -   Fee: 0.******** EUR
2025-06-26 13:29:35,786 - root - INFO - TRADE SUCCESS - TRX/EUR: Successfully updated current asset
2025-06-26 13:29:35,791 - root - INFO - Trade executed: BUY TRX/EUR, amount=42.********, price=0.********, filled=42.********
2025-06-26 13:29:35,792 - root - INFO -   Fee: 0.******** EUR
2025-06-26 13:29:35,792 - root - INFO - Single-asset trade result logged to trade log file
2025-06-26 13:29:35,792 - root - INFO - Trade executed: {'success': True, 'symbol': 'TRX/EUR', 'side': 'buy', 'amount': 42.731372127979384, 'price': 0.23285, 'order': {'id': 'paper-1750937375-TRX/EUR-buy-42.********207215', 'symbol': 'TRX/EUR', 'side': 'buy', 'type': 'market', 'amount': 42.********207215, 'price': 0.23285, 'cost': 9.90025, 'fee': {'cost': 0.********, 'currency': 'EUR', 'rate': 0.001}, 'status': 'closed', 'filled': 42.********207215, 'remaining': 0, 'timestamp': 1750937375779, 'datetime': '2025-06-26T13:29:35.779981', 'trades': [], 'average': 0.23285, 'average_price': 0.23285}, 'filled_amount': 42.********207215, 'fee': {'cost': 0.********, 'currency': 'EUR', 'rate': 0.001}, 'quote_currency': 'EUR', 'base_currency': 'TRX', 'timestamp': '2025-06-26T13:29:35.780984'}
2025-06-26 13:29:35,860 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 13:29:35,867 - root - INFO - Asset selection logged: 1 assets selected with single_asset allocation
2025-06-26 13:29:35,868 - root - INFO - Asset scores (sorted by score):
2025-06-26 13:29:35,868 - root - INFO -   BTC/EUR: score=12.0, status=NOT SELECTED, weight=0.00
2025-06-26 13:29:35,868 - root - INFO -   TRX/EUR: score=12.0, status=SELECTED, weight=1.00
2025-06-26 13:29:35,868 - root - INFO -   BNB/EUR: score=11.0, status=NOT SELECTED, weight=0.00
2025-06-26 13:29:35,868 - root - INFO -   XRP/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-06-26 13:29:35,870 - root - INFO -   AAVE/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-06-26 13:29:35,870 - root - INFO -   ETH/EUR: score=8.0, status=NOT SELECTED, weight=0.00
2025-06-26 13:29:35,870 - root - INFO -   SOL/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-06-26 13:29:35,870 - root - INFO -   LINK/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-06-26 13:29:35,871 - root - INFO -   AVAX/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-26 13:29:35,871 - root - INFO -   ADA/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-26 13:29:35,871 - root - INFO -   DOGE/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-26 13:29:35,872 - root - INFO -   SUI/EUR: score=2.0, status=NOT SELECTED, weight=0.00
2025-06-26 13:29:35,872 - root - INFO -   DOT/EUR: score=1.0, status=NOT SELECTED, weight=0.00
2025-06-26 13:29:35,872 - root - INFO -   PEPE/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-06-26 13:29:35,872 - root - INFO - Rejected assets:
2025-06-26 13:29:35,872 - root - INFO -   BTC/EUR: reason=Failed to trade, score=12.0, rank=1
2025-06-26 13:29:35,872 - root - WARNING -   HIGH-SCORING ASSET REJECTED: BTC/EUR (rank 1, score 12.0) - Failed to trade
2025-06-26 13:29:35,872 - root - INFO - Asset selection logged with 14 assets scored and 1 assets selected
2025-06-26 13:29:35,872 - root - INFO - MTPI disabled - skipping MTPI score extraction
2025-06-26 13:29:35,873 - root - INFO - Extracted asset scores: {'ETH/EUR': 8.0, 'BTC/EUR': 12.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 3.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-26 13:29:35,906 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 13:29:35,907 - root - INFO - Strategy execution completed successfully in 19.15 seconds
2025-06-26 13:29:35,912 - root - INFO - Saved recovery state to data/state\recovery_state.json
2025-06-26 13:29:36,278 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:29:46,303 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:29:56,334 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:30:06,346 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:30:16,359 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:30:26,376 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:30:36,393 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:30:46,401 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:30:56,419 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:31:06,431 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:31:16,445 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:31:26,456 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:31:36,471 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:31:46,484 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:31:56,499 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:32:06,522 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:32:16,538 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:32:26,558 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:32:36,572 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:32:46,602 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:32:56,616 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:33:06,642 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:33:16,657 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:33:26,671 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:33:36,684 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:33:46,722 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:33:56,761 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:34:06,773 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:34:16,805 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:34:26,823 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:34:36,840 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:34:46,860 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:34:56,878 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:35:06,895 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:35:16,922 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:35:26,936 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:35:36,958 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:35:46,979 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:35:56,994 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:36:07,007 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:36:17,025 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:36:27,038 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:36:37,056 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:36:47,151 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:36:57,167 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:37:07,193 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:37:17,213 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:37:27,227 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:37:37,245 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:37:47,274 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:37:57,301 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:38:07,317 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:38:17,334 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:38:27,346 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:38:37,366 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:38:47,383 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:38:57,399 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:39:07,419 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:39:17,451 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:39:27,467 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:39:37,484 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:39:47,502 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:39:57,529 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:40:07,544 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:40:17,574 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:40:27,587 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:40:37,604 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:40:47,634 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:40:57,651 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:41:07,690 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:41:17,747 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:41:27,764 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:41:37,795 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:41:47,820 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:41:57,834 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:42:07,848 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:42:17,865 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:42:27,880 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:42:37,905 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:42:47,933 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:42:57,967 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:43:08,009 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:43:18,037 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:43:28,057 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:43:38,071 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:43:48,086 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:43:58,104 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:44:08,133 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:44:18,163 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:44:28,183 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:44:38,202 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:44:48,227 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:44:58,249 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:45:08,266 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:45:18,282 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:45:28,295 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:45:38,319 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:45:48,349 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:45:58,376 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:46:08,501 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:46:18,517 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:46:28,532 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:46:38,549 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:46:48,565 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:46:58,577 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:47:08,593 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:47:18,624 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:47:28,639 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:47:38,658 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:47:48,686 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:47:58,717 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:48:08,732 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:48:18,752 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:48:28,768 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:48:38,782 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:48:48,801 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:48:58,818 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:49:08,836 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:49:18,854 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:49:28,877 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:49:38,899 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:49:48,916 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:49:58,934 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:50:08,952 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:50:18,967 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:50:28,984 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:50:39,000 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:50:49,030 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:50:59,052 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:51:09,070 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:51:19,083 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:51:29,100 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:51:39,113 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:51:49,127 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:51:59,144 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:52:09,159 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:52:19,175 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:52:29,192 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:52:39,208 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:52:49,237 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:52:59,254 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:53:09,268 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:53:19,288 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:53:29,302 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:53:39,313 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:53:49,327 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:53:59,337 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:54:09,350 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:54:19,370 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:54:29,384 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:54:39,396 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:54:49,408 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:54:59,430 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:55:09,445 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:55:19,457 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:55:29,474 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:55:39,510 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:55:49,554 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:55:59,568 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:56:09,576 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:56:19,596 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:56:29,841 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:56:39,969 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:56:49,984 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:57:00,019 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:57:10,030 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:57:20,045 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:57:30,059 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:57:40,071 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:57:50,084 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:58:00,157 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:58:10,178 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:58:20,194 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:58:30,231 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:58:40,242 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:58:50,260 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:59:00,277 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:59:10,291 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:59:20,311 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:59:30,327 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:59:40,351 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 13:59:50,363 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:00:00,378 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:00:10,391 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:00:20,406 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:00:30,421 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:00:40,433 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:00:50,446 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:01:00,464 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:01:10,487 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:01:20,509 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:01:30,520 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:01:40,533 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:01:50,545 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:02:00,556 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:02:10,567 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:02:20,581 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:02:30,593 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:02:40,612 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:02:50,675 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:03:00,689 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:03:10,805 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:03:20,820 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:03:30,834 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:03:40,850 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:03:50,868 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:04:00,959 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:04:10,977 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:04:20,992 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:04:31,011 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:04:41,027 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:04:51,042 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:05:01,051 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:05:11,061 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:05:21,075 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:05:31,092 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:05:41,107 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:05:51,120 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:06:01,130 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:06:11,143 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:06:21,159 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:06:31,172 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:06:41,184 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:06:51,197 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:07:01,210 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:07:11,222 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:07:21,236 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:07:31,250 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:07:41,265 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:07:51,281 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:08:01,292 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:08:11,304 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:08:21,324 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:08:31,337 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:08:41,364 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:08:51,384 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:09:01,399 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:09:11,412 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:09:21,429 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:09:31,443 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:09:41,459 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:09:51,474 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:10:01,490 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:10:11,509 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:10:21,521 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:10:31,534 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:10:41,556 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:10:51,569 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
